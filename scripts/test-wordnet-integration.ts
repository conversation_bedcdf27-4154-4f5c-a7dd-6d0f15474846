#!/usr/bin/env tsx

/**
 * <PERSON><PERSON><PERSON> to test WordNet integration in generate words functionality
 * This script demonstrates how the new WordNet-based word generation works
 */

import { PrismaClient, Language } from '@prisma/client';

const prisma = new PrismaClient();

// Mock WordNet data for testing
const mockWords = [
	{
		term: 'animal',
		language: Language.EN,
		wordnet_data: {
			synsets: ['a living organism that feeds on organic matter'],
			lemma: 'animal',
			hypernyms: ['organism', 'being'],
			hyponyms: ['mammal', 'bird', 'fish', 'reptile'],
			holonyms: ['kingdom'],
			meronyms: ['body', 'limb'],
		},
	},
	{
		term: 'dog',
		language: Language.EN,
		wordnet_data: {
			synsets: ['a member of the genus Canis', 'domestic animal'],
			lemma: 'dog',
			hypernyms: ['animal', 'mammal', 'canine'],
			hyponyms: ['puppy', 'hound', 'terrier'],
			holonyms: ['pack'],
			meronyms: ['tail', 'paw', 'snout'],
		},
	},
	{
		term: 'cat',
		language: Language.EN,
		wordnet_data: {
			synsets: ['feline mammal', 'domestic cat'],
			lemma: 'cat',
			hypernyms: ['animal', 'mammal', 'feline'],
			hyponyms: ['kitten', 'tomcat'],
			holonyms: ['pride'],
			meronyms: ['whisker', 'claw', 'tail'],
		},
	},
	{
		term: 'vehicle',
		language: Language.EN,
		wordnet_data: {
			synsets: ['a conveyance that transports people or objects'],
			lemma: 'vehicle',
			hypernyms: ['conveyance', 'transport'],
			hyponyms: ['car', 'truck', 'bicycle', 'motorcycle'],
			holonyms: ['transportation system'],
			meronyms: ['wheel', 'engine', 'brake'],
		},
	},
	{
		term: 'car',
		language: Language.EN,
		wordnet_data: {
			synsets: ['a motor vehicle with four wheels'],
			lemma: 'car',
			hypernyms: ['vehicle', 'motor vehicle'],
			hyponyms: ['sedan', 'coupe', 'hatchback'],
			holonyms: ['traffic'],
			meronyms: ['door', 'window', 'seat'],
		},
	},
];

// Test functions that mirror the actual implementation
function estimateDifficulty(word: any): 'EASY' | 'MEDIUM' | 'HARD' {
	const termLength = word.term.length;
	const hasComplexWordNet = word.wordnet_data && 
		(word.wordnet_data.synsets.length > 3 || 
		 word.wordnet_data.hypernyms.length > 2);

	if (termLength <= 4 && !hasComplexWordNet) {
		return 'EASY';
	} else if (termLength <= 8) {
		return 'MEDIUM';
	} else {
		return 'HARD';
	}
}

function filterWordsBySemanticData(words: any[], keywords: string[]) {
	return words.filter((word) => {
		if (!word.wordnet_data) return false;

		const keywordLower = keywords.map(k => k.toLowerCase());
		
		// Combine all WordNet semantic data
		const semanticData = [
			...(word.wordnet_data.synsets || []),
			...(word.wordnet_data.hypernyms || []),
			...(word.wordnet_data.hyponyms || []),
			...(word.wordnet_data.holonyms || []),
			...(word.wordnet_data.meronyms || []),
		].map(data => data.toLowerCase());

		// Check if any keyword matches any semantic data
		return keywordLower.some(keyword => 
			semanticData.some(data => 
				data.includes(keyword) || keyword.includes(data)
			)
		);
	});
}

function shuffleArray<T>(array: T[]): T[] {
	const shuffled = [...array];
	for (let i = shuffled.length - 1; i > 0; i--) {
		const j = Math.floor(Math.random() * (i + 1));
		[shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
	}
	return shuffled;
}

async function testWordNetIntegration() {
	console.log('🧪 Testing WordNet Integration for Generate Words\n');

	// Test 1: Difficulty estimation
	console.log('📊 Test 1: Difficulty Estimation');
	mockWords.forEach(word => {
		const difficulty = estimateDifficulty(word);
		console.log(`  ${word.term} (${word.term.length} chars) -> ${difficulty}`);
	});
	console.log();

	// Test 2: Semantic filtering
	console.log('🔍 Test 2: Semantic Data Filtering');
	
	const testKeywords = ['animal', 'vehicle', 'transport'];
	testKeywords.forEach(keyword => {
		console.log(`  Keyword: "${keyword}"`);
		const matches = filterWordsBySemanticData(mockWords, [keyword]);
		matches.forEach(match => {
			console.log(`    ✓ ${match.term} (found in: ${
				match.wordnet_data.synsets.concat(
					match.wordnet_data.hypernyms,
					match.wordnet_data.hyponyms
				).filter((data: string) => 
					data.toLowerCase().includes(keyword.toLowerCase()) ||
					keyword.toLowerCase().includes(data.toLowerCase())
				).join(', ')
			})`);
		});
		console.log();
	});

	// Test 3: Generate words simulation
	console.log('🎯 Test 3: Generate Words Simulation');
	const keywords = ['animal'];
	const maxTerms = 3;
	
	console.log(`  Keywords: ${keywords.join(', ')}`);
	console.log(`  Max terms: ${maxTerms}`);
	
	// Step 1: Filter by keywords
	const filteredWords = filterWordsBySemanticData(mockWords, keywords);
	console.log(`  Found ${filteredWords.length} words matching keywords`);
	
	// Step 2: Convert to RandomWord format
	const randomWords = filteredWords.map(word => ({
		term: word.term,
		language: word.language,
		difficulty: estimateDifficulty(word),
	}));
	
	// Step 3: Shuffle and limit
	const shuffledWords = shuffleArray(randomWords);
	const finalWords = shuffledWords.slice(0, maxTerms);
	
	console.log('  Generated words:');
	finalWords.forEach((word, index) => {
		console.log(`    ${index + 1}. ${word.term} (${word.difficulty})`);
	});
	console.log();

	// Test 4: Database integration simulation
	console.log('💾 Test 4: Database Integration Simulation');
	console.log('  This would involve:');
	console.log('  1. searchWordsWithWordNet() - find words containing keywords');
	console.log('  2. searchWordsBySemanticData() - find words with semantic relationships');
	console.log('  3. Combine and deduplicate results');
	console.log('  4. Fall back to LLM if insufficient words found');
	console.log();

	console.log('✅ WordNet Integration Test Complete!');
	console.log('\n📝 Summary:');
	console.log('  - Words are now generated from database WordNet data first');
	console.log('  - Semantic relationships (synsets, hypernyms, etc.) are used for matching');
	console.log('  - Difficulty is estimated based on word length and WordNet complexity');
	console.log('  - LLM is used as fallback when database doesn\'t have enough words');
}

// Run the test
testWordNetIntegration()
	.then(() => {
		console.log('\n🎉 Test completed successfully!');
		process.exit(0);
	})
	.catch((error) => {
		console.error('❌ Test failed:', error);
		process.exit(1);
	});
