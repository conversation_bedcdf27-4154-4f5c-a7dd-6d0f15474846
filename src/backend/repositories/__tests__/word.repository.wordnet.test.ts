import { WordRepositoryImpl } from '../word.repository';
import { Language } from '@prisma/client';
import { PrismaClient } from '@prisma/client';

// Mock Prisma
const mockPrisma = {
	word: {
		findMany: jest.fn(),
	},
} as any;

describe('WordRepository - WordNet Integration', () => {
	let wordRepository: WordRepositoryImpl;

	beforeEach(() => {
		wordRepository = new WordRepositoryImpl(mockPrisma as PrismaClient);
		jest.clearAllMocks();
	});

	describe('searchWordsBySemanticData', () => {
		it('should return empty array for empty keywords', async () => {
			const result = await wordRepository.searchWordsBySemanticData([]);
			expect(result).toEqual([]);
			expect(mockPrisma.word.findMany).not.toHaveBeenCalled();
		});

		it('should search and filter words by semantic data', async () => {
			const mockWords = [
				{
					id: '1',
					term: 'dog',
					language: Language.EN,
					wordnet_data: {
						synsets: ['domestic animal', 'pet'],
						hypernyms: ['animal', 'mammal'],
						hyponyms: ['puppy'],
						holonyms: [],
						meronyms: [],
					},
				},
				{
					id: '2',
					term: 'cat',
					language: Language.EN,
					wordnet_data: {
						synsets: ['feline', 'pet'],
						hypernyms: ['animal', 'mammal'],
						hyponyms: ['kitten'],
						holonyms: [],
						meronyms: [],
					},
				},
				{
					id: '3',
					term: 'car',
					language: Language.EN,
					wordnet_data: {
						synsets: ['vehicle', 'automobile'],
						hypernyms: ['vehicle'],
						hyponyms: [],
						holonyms: [],
						meronyms: ['engine', 'wheel'],
					},
				},
			];

			mockPrisma.word.findMany.mockResolvedValue(mockWords);

			const result = await wordRepository.searchWordsBySemanticData(['animal'], Language.EN, 10);

			expect(mockPrisma.word.findMany).toHaveBeenCalledWith({
				where: {
					language: Language.EN,
					wordnet_data: {
						isNot: null,
					},
				},
				include: expect.any(Object),
				take: 50, // 10 * 5
			});

			// Should return dog and cat (both have 'animal' in hypernyms)
			expect(result).toHaveLength(2);
			expect(result.map(w => w.term)).toEqual(expect.arrayContaining(['dog', 'cat']));
		});

		it('should match keywords in synsets', async () => {
			const mockWords = [
				{
					id: '1',
					term: 'vehicle',
					language: Language.EN,
					wordnet_data: {
						synsets: ['means of transportation', 'conveyance'],
						hypernyms: [],
						hyponyms: ['car', 'truck'],
						holonyms: [],
						meronyms: [],
					},
				},
			];

			mockPrisma.word.findMany.mockResolvedValue(mockWords);

			const result = await wordRepository.searchWordsBySemanticData(['transportation'], Language.EN, 10);

			// Should match because 'transportation' is in synsets
			expect(result).toHaveLength(1);
			expect(result[0].term).toBe('vehicle');
		});

		it('should sort by WordNet data richness', async () => {
			const mockWords = [
				{
					id: '1',
					term: 'simple',
					language: Language.EN,
					wordnet_data: {
						synsets: ['easy'],
						hypernyms: [],
						hyponyms: [],
						holonyms: [],
						meronyms: [],
					},
				},
				{
					id: '2',
					term: 'complex',
					language: Language.EN,
					wordnet_data: {
						synsets: ['complicated', 'intricate', 'elaborate'],
						hypernyms: ['difficult', 'hard'],
						hyponyms: [],
						holonyms: [],
						meronyms: [],
					},
				},
			];

			mockPrisma.word.findMany.mockResolvedValue(mockWords);

			const result = await wordRepository.searchWordsBySemanticData(['test'], Language.EN, 10);

			// 'complex' should come first because it has more WordNet data
			expect(result[0].term).toBe('complex');
			expect(result[1].term).toBe('simple');
		});

		it('should handle partial keyword matches', async () => {
			const mockWords = [
				{
					id: '1',
					term: 'transportation',
					language: Language.EN,
					wordnet_data: {
						synsets: ['means of transport'],
						hypernyms: [],
						hyponyms: [],
						holonyms: [],
						meronyms: [],
					},
				},
			];

			mockPrisma.word.findMany.mockResolvedValue(mockWords);

			const result = await wordRepository.searchWordsBySemanticData(['transport'], Language.EN, 10);

			// Should match because 'transport' is contained in 'means of transport'
			expect(result).toHaveLength(1);
			expect(result[0].term).toBe('transportation');
		});

		it('should respect language filter', async () => {
			const result = await wordRepository.searchWordsBySemanticData(['test'], Language.VI, 10);

			expect(mockPrisma.word.findMany).toHaveBeenCalledWith({
				where: {
					language: Language.VI,
					wordnet_data: {
						isNot: null,
					},
				},
				include: expect.any(Object),
				take: 50,
			});
		});

		it('should work without language filter', async () => {
			const result = await wordRepository.searchWordsBySemanticData(['test'], undefined, 10);

			expect(mockPrisma.word.findMany).toHaveBeenCalledWith({
				where: {
					wordnet_data: {
						isNot: null,
					},
				},
				include: expect.any(Object),
				take: 50,
			});
		});

		it('should respect limit parameter', async () => {
			const mockWords = Array.from({ length: 30 }, (_, i) => ({
				id: `${i + 1}`,
				term: `word${i + 1}`,
				language: Language.EN,
				wordnet_data: {
					synsets: ['test'],
					hypernyms: [],
					hyponyms: [],
					holonyms: [],
					meronyms: [],
				},
			}));

			mockPrisma.word.findMany.mockResolvedValue(mockWords);

			const result = await wordRepository.searchWordsBySemanticData(['test'], Language.EN, 5);

			expect(result).toHaveLength(5);
		});
	});
});
