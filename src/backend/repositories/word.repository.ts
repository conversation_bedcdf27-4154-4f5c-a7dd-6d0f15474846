import type { Language, Prisma, PrismaClient, Word } from '@prisma/client';
import { type BaseRepository, BaseRepositoryImpl } from './base.repository';

// Define the include structure for Word queries
export const wordInclude = {
	definitions: {
		include: {
			explains: true,
			examples: true,
		},
	},
	wordnet_data: true,
};

export interface WordRepository extends BaseRepository<Word> {
	find(query: Record<string, unknown>, limit?: number): Promise<Word[]>;
	searchWords(term: string, language?: Language, limit?: number): Promise<Word[]>;
	searchWordsWithWordNet(term: string, language?: Language, limit?: number): Promise<Word[]>;
	searchWordsBySemanticData(
		keywords: string[],
		language?: Language,
		limit?: number
	): Promise<Word[]>;
	findOrCreateWords(terms: string[], language: Language): Promise<Word[]>;
	findWordsByIds(wordIds: string[]): Promise<Word[]>;
	findByTerm(term: string, language: Language): Promise<Word | null>;
	upsert(data: Prisma.WordCreateInput): Promise<Word>;
	createDefinition(data: Prisma.DefinitionCreateInput): Promise<any>;
	addExamplesToDefinition(
		definitionId: string,
		examples: Array<{ EN: string; VI: string }>
	): Promise<void>;
	createOrUpdateWordNetData(
		wordId: string,
		wordNetData: {
			synsets: string[];
			lemma: string | null;
			hypernyms: string[];
			hyponyms: string[];
			holonyms: string[];
			meronyms: string[];
		}
	): Promise<void>;
}

export class WordRepositoryImpl extends BaseRepositoryImpl<Word> implements WordRepository {
	constructor(private readonly prisma: PrismaClient) {
		super(prisma.word);
	}

	override async findById(id: string): Promise<Word | null> {
		const word = await this.prisma.word.findUnique({
			where: { id },
			include: wordInclude,
		});
		return word;
	}

	override async findOne(query: Record<string, unknown>): Promise<Word | null> {
		const word = await this.prisma.word.findFirst({
			where: query,
			include: wordInclude,
		});
		return word;
	}

	override async find(query: Prisma.WordWhereInput, limit?: number): Promise<Word[]> {
		const words = await this.prisma.word.findMany({
			where: query,
			include: wordInclude,
			take: limit,
		});
		return words;
	}

	override async create(data: Prisma.WordCreateInput): Promise<Word> {
		if (!data.term || !data.language) {
			throw new Error('Term and language are required');
		}

		const word = await this.prisma.word.create({
			data,
			include: wordInclude,
		});
		return word;
	}

	async upsert(data: Prisma.WordCreateInput): Promise<Word> {
		if (!data.term || !data.language) {
			throw new Error('Term and language are required');
		}

		const word = await this.prisma.word.upsert({
			where: {
				term_language: {
					term: data.term,
					language: data.language,
				},
			},
			update: {
				// Update fields if needed (excluding term and language which are unique)
				...(data.definitions && { definitions: data.definitions }),
			},
			create: data,
			include: wordInclude,
		});
		return word;
	}

	override async update(id: string, data: Prisma.WordUpdateInput): Promise<Word> {
		const word = await this.prisma.word.update({
			where: { id },
			data,
			include: wordInclude,
		});
		return word;
	}

	override async delete(query: Record<string, unknown>): Promise<void> {
		await this.prisma.word.deleteMany({
			where: query,
		});
	}

	async searchWords(term: string, language?: Language, limit = 10): Promise<Word[]> {
		const words = await this.prisma.word.findMany({
			where: {
				term: {
					contains: term,
					mode: 'insensitive',
				},
				...(language && { language }),
			},
			include: wordInclude,
			take: limit,
		});
		return words;
	}

	async searchWordsWithWordNet(term: string, language?: Language, limit = 10): Promise<Word[]> {
		const words = await this.prisma.word.findMany({
			where: {
				term: {
					contains: term,
					mode: 'insensitive',
				},
				...(language && { language }),
				// Only return words that have WordNet data
				wordnet_data: {
					isNot: null,
				},
			},
			include: wordInclude,
			take: limit,
			orderBy: [
				// Prioritize exact matches
				{
					term: 'asc',
				},
			],
		});
		return words;
	}

	async searchWordsBySemanticData(
		keywords: string[],
		language?: Language,
		limit = 20
	): Promise<Word[]> {
		if (keywords.length === 0) {
			return [];
		}

		// Get all words with WordNet data first, then filter in memory
		// This is simpler and more reliable than complex SQL queries
		const allWords = await this.prisma.word.findMany({
			where: {
				...(language && { language }),
				wordnet_data: {
					isNot: null,
				},
			},
			include: wordInclude,
			take: limit * 5, // Get more words to filter through
		});

		// Filter words that match any of the keywords in their WordNet data
		const matchingWords = allWords.filter((word) => {
			if (!word.wordnet_data) return false;

			const keywordLower = keywords.map((k) => k.toLowerCase());

			// Combine all WordNet semantic data
			const semanticData = [
				...(word.wordnet_data.synsets || []),
				...(word.wordnet_data.hypernyms || []),
				...(word.wordnet_data.hyponyms || []),
				...(word.wordnet_data.holonyms || []),
				...(word.wordnet_data.meronyms || []),
			].map((data) => data.toLowerCase());

			// Check if any keyword matches any semantic data
			return keywordLower.some((keyword) =>
				semanticData.some((data) => data.includes(keyword) || keyword.includes(data))
			);
		});

		// Sort by relevance (words with more WordNet data first)
		const sortedWords = matchingWords.sort((a, b) => {
			const aDataCount =
				(a.wordnet_data?.synsets?.length || 0) + (a.wordnet_data?.hypernyms?.length || 0);
			const bDataCount =
				(b.wordnet_data?.synsets?.length || 0) + (b.wordnet_data?.hypernyms?.length || 0);

			if (aDataCount !== bDataCount) {
				return bDataCount - aDataCount; // More data first
			}

			return a.term.localeCompare(b.term); // Alphabetical as secondary sort
		});

		return sortedWords.slice(0, limit);
	}

	async findOrCreateWords(terms: string[], language: Language): Promise<Word[]> {
		const existingWords = await this.prisma.word.findMany({
			where: {
				term: {
					in: terms,
				},
				language,
			},
			include: wordInclude,
		});

		const existingTerms = new Set(existingWords.map((w) => w.term));
		const newTerms = terms.filter((term) => !existingTerms.has(term));

		if (newTerms.length === 0) {
			return existingWords;
		}

		const newWords = await Promise.all(
			newTerms.map((term) =>
				this.prisma.word.create({
					data: {
						term,
						language,
					},
					include: wordInclude,
				})
			)
		);

		return [...existingWords, ...newWords];
	}

	async findWordsByIds(wordIds: string[]): Promise<Word[]> {
		const words = await this.prisma.word.findMany({
			where: {
				id: {
					in: wordIds,
				},
			},
			include: wordInclude,
		});
		return words;
	}

	async findByTerm(term: string, language: Language): Promise<Word | null> {
		const word = await this.prisma.word.findFirst({
			where: {
				term,
				language,
			},
			include: wordInclude,
		});
		return word;
	}

	async createDefinition(data: Prisma.DefinitionCreateInput): Promise<any> {
		const definition = await this.prisma.definition.create({
			data,
			include: {
				explains: true,
				examples: true,
			},
		});
		return definition;
	}

	async addExamplesToDefinition(
		definitionId: string,
		examples: Array<{ EN: string; VI: string }>
	): Promise<void> {
		await this.prisma.example.createMany({
			data: examples.map((example) => ({
				EN: example.EN,
				VI: example.VI,
				definition_id: definitionId,
			})),
		});
	}

	async createOrUpdateWordNetData(
		wordId: string,
		wordNetData: {
			synsets: string[];
			lemma: string | null;
			hypernyms: string[];
			hyponyms: string[];
			holonyms: string[];
			meronyms: string[];
		}
	): Promise<void> {
		await this.prisma.wordNetData.upsert({
			where: { word_id: wordId },
			update: {
				synsets: wordNetData.synsets,
				lemma: wordNetData.lemma,
				hypernyms: wordNetData.hypernyms,
				hyponyms: wordNetData.hyponyms,
				holonyms: wordNetData.holonyms,
				meronyms: wordNetData.meronyms,
			},
			create: {
				word_id: wordId,
				synsets: wordNetData.synsets,
				lemma: wordNetData.lemma,
				hypernyms: wordNetData.hypernyms,
				hyponyms: wordNetData.hyponyms,
				holonyms: wordNetData.holonyms,
				meronyms: wordNetData.meronyms,
			},
		});
	}
}
