import { Language } from '@prisma/client';

// Simple unit tests for WordNet integration logic
describe('WordNet Integration Logic', () => {
	// Test the difficulty estimation logic
	describe('estimateDifficulty', () => {
		const estimateDifficulty = (word: any): 'EASY' | 'MEDIUM' | 'HARD' => {
			const termLength = word.term.length;
			const hasComplexWordNet = word.wordnet_data && 
				(word.wordnet_data.synsets.length > 3 || 
				 word.wordnet_data.hypernyms.length > 2);

			if (termLength <= 4 && !hasComplexWordNet) {
				return 'EASY';
			} else if (termLength <= 8) {
				return 'MEDIUM';
			} else {
				return 'HARD';
			}
		};

		it('should classify short words as EASY', () => {
			const word = {
				term: 'cat',
				wordnet_data: {
					synsets: ['pet'],
					hypernyms: [],
				},
			};

			expect(estimateDifficulty(word)).toBe('EASY');
		});

		it('should classify medium words as MEDIUM', () => {
			const word = {
				term: 'animal',
				wordnet_data: {
					synsets: ['living thing'],
					hypernyms: ['organism'],
				},
			};

			expect(estimateDifficulty(word)).toBe('MEDIUM');
		});

		it('should classify long words as HARD', () => {
			const word = {
				term: 'extraordinary',
				wordnet_data: {
					synsets: ['remarkable'],
					hypernyms: [],
				},
			};

			expect(estimateDifficulty(word)).toBe('HARD');
		});
	});

	// Test semantic data filtering logic
	describe('semanticDataFiltering', () => {
		const filterWordsBySemanticData = (words: any[], keywords: string[]) => {
			return words.filter((word) => {
				if (!word.wordnet_data) return false;

				const keywordLower = keywords.map(k => k.toLowerCase());
				
				// Combine all WordNet semantic data
				const semanticData = [
					...(word.wordnet_data.synsets || []),
					...(word.wordnet_data.hypernyms || []),
					...(word.wordnet_data.hyponyms || []),
					...(word.wordnet_data.holonyms || []),
					...(word.wordnet_data.meronyms || []),
				].map(data => data.toLowerCase());

				// Check if any keyword matches any semantic data
				return keywordLower.some(keyword => 
					semanticData.some(data => 
						data.includes(keyword) || keyword.includes(data)
					)
				);
			});
		};

		it('should filter words by synsets', () => {
			const words = [
				{
					term: 'dog',
					wordnet_data: {
						synsets: ['domestic animal', 'pet'],
						hypernyms: [],
						hyponyms: [],
						holonyms: [],
						meronyms: [],
					},
				},
				{
					term: 'car',
					wordnet_data: {
						synsets: ['vehicle'],
						hypernyms: [],
						hyponyms: [],
						holonyms: [],
						meronyms: [],
					},
				},
			];

			const result = filterWordsBySemanticData(words, ['animal']);
			expect(result).toHaveLength(1);
			expect(result[0].term).toBe('dog');
		});

		it('should filter words by hypernyms', () => {
			const words = [
				{
					term: 'dog',
					wordnet_data: {
						synsets: ['canine'],
						hypernyms: ['animal', 'mammal'],
						hyponyms: [],
						holonyms: [],
						meronyms: [],
					},
				},
			];

			const result = filterWordsBySemanticData(words, ['animal']);
			expect(result).toHaveLength(1);
			expect(result[0].term).toBe('dog');
		});
	});
});
