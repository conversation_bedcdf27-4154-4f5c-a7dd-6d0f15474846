import { getDetailedErrorRequirements } from '@/app/collections/[id]/paragraph/grammar-practice/constants';
import { getLLMConfig, getLLMOptimizationConfig } from '@/config';
import { RandomWord, RandomWordDetailSchema, RandomWordSchema } from '@/models';
import { Difficulty, Language, Word, PartsOfSpeech } from '@prisma/client';
import OpenAI from 'openai';
import { zodResponseFormat } from 'openai/helpers/zod';
import { z } from 'zod';

// Schema for OpenAI response (without WordNet data to avoid .optional() issues)
const OpenAIWordDetailSchema = z.object({
	term: z.string(),
	language: z.nativeEnum(Language),
	definitions: z.array(
		z.object({
			pos: z.array(z.nativeEnum(PartsOfSpeech)),
			ipa: z.string(),
			explains: z.array(
				z.object({
					EN: z.string(),
					VI: z.string(),
				})
			),
			examples: z.array(
				z.object({
					EN: z.string(),
					VI: z.string(),
				})
			),
		})
	),
});

// Enhanced difficulty specifications interface
interface DifficultySpecs {
	vocabulary: {
		level: string;
		wordCount: number;
		complexity: string;
	};
	grammar: {
		structures: string[];
		complexity: string;
	};
	sentenceLength: {
		min: number;
		max: number;
	};
	topics: string[];
	errorTypes: string[];
}

// Enhanced validation interface
interface ValidationResult {
	isValid: boolean;
	errors: string[];
	suggestions: string[];
}

// Schema for additional examples generation
const AdditionalExamplesSchema = z.object({
	examples: z.array(
		z.object({
			EN: z.string(),
			VI: z.string(),
		})
	),
});
import { WordService } from '.';
import { batchProcessor } from './batch-processor.service';
import { GeminiService } from './gemini.service';
import { modelSelector } from './model-selector.service';
import { PromptOptimizerService } from './prompt-optimizer.service';
import { semanticCache, SemanticCacheService } from './semantic-cache.service';
import { tokenMonitor } from './token-monitor.service';

// Interface for existing method, updated
export interface GenerateParagraphParams {
	keywords: string[];
	language: Language;
	difficulty: Difficulty;
	count: number; // Added count for multiple paragraphs
	sentenceCount?: number; // desired number of sentences
}

// Interfaces for existing methods (remains the same, but listed for completeness)
interface GenerateRandomTermsParams {
	userId: string;
	keywordTerms: string[];
	excludesTerms: string[];
	maxTerms: number;
	excludeCollectionIds: string[];
	source_language: Language;
	target_language: Language;
}

interface GenerateAdditionalExamplesParams {
	term: string;
	source_language: Language;
	target_language: Language;
	existingExamples?: Array<{ EN: string; VI: string }>;
	count?: number;
}

interface GenerateExercisesParams {
	paragraph: string;
	keywords: string[];
	language: Language;
	difficulty: Difficulty;
}

export interface AnalyzeParagraphRequest {
	content: string;
	language: Language;
}

// Interfaces for Question & Answer features
export interface GenerateQuestionsParams {
	paragraph: string;
	language: Language;
	questionCount: number;
}

// Interface for combined paragraph and questions generation
export interface GenerateParagraphWithQuestionsParams {
	keywords: string[];
	language: Language;
	difficulty: Difficulty;
	sentenceCount?: number;
	questionCount: number;
}

export interface ParagraphWithQuestionsResult {
	paragraph: string;
	questions: string[];
}

export interface EvaluateAnswersParams {
	paragraph: string;
	questions: string[];
	answers: string[];
	qna_language: Language; // Language of the questions and answers
	feedback_native_language: Language; // User's native language for feedback
}

export interface GrammarPracticeParams {
	keywords: string[];
	language: Language;
	source_language: Language;
	target_language: Language;
	difficulty: Difficulty;
	count: number;
	sentenceCount?: number;
	errorDensity?: 'low' | 'medium' | 'high';
}

// Enhanced word-level structure for grammar practice
export interface WordToken {
	text: string;
	position: number; // Position in the sentence
	isError: boolean;
	errorType: string | null;
	correctedText: string | null;
	explanation: {
		source_language: string;
		target_language: string;
	} | null;
}

export interface GrammarPracticeResultItem {
	// Original structure for backward compatibility
	paragraphWithErrors: string;
	correctedParagraph: string;

	// Enhanced word-level structure
	wordTokens: WordToken[];

	// Detailed error information
	allErrors: Array<{
		errorText: string;
		correctedText: string;
		errorType: string;
		startPosition: number; // Start position in original text
		endPosition: number; // End position in original text
		explanation: {
			source_language: string;
			target_language: string;
		};
	}>;

	// Summary information
	summary: {
		totalWords: number;
		totalErrors: number;
		errorTypes: string[];
	};
}

export interface AnswerEvaluationResult {
	question: string;
	answer: string;
	feedback: {
		qna_feedback_text: string; // Feedback in the Q&A language
		native_feedback_text: string; // Feedback in the user's native language
	};
	score: number | null;
	is_correct: boolean | null;
	suggested_answer: string | null;
}

// Interfaces for the new evaluateTranslation method
export interface EvaluateTranslationParams {
	original_text: string;
	translated_text: string;
	source_language: Language;
	target_language: Language;
}

export interface TranslationEvaluationResult {
	feedback: {
		source_language: string;
		target_language: string;
	};
	score: number | null;
	suggestions: {
		source_language: string[] | null;
		target_language: string[] | null;
	} | null;
}

// Zod schemas for new/updated methods
const ExerciseSchema = z.object({
	type: z.string(),
	question: z.string(),
	answer: z.string(),
	options: z.array(z.string()).nullable(),
	explanation: z.string().nullable(),
});

const ExercisesResponseSchema = z.object({
	exercises: z.array(ExerciseSchema),
});

// Zod schema for generating questions
const GeneratedQuestionsSchema = z.object({
	questions: z
		.array(z.string())
		.describe('An array of generated questions based on the paragraph.'),
});

// Zod schema for combined paragraph and questions generation
const ParagraphWithQuestionsSchema = z.object({
	paragraph: z.string().describe('A generated paragraph based on the provided keywords.'),
	questions: z
		.array(z.string())
		.describe('An array of generated questions based on the paragraph.'),
});

// Zod schema for evaluating a single answer
const AnswerEvaluationSchema = z.object({
	question: z.string().describe('The original question this evaluation pertains to.'),
	answer: z.string().describe("The user's answer that was evaluated."),
	feedback: z
		.object({
			qna_feedback_text: z
				.string()
				.describe(
					"Detailed feedback in the Q&A language ({qna_language}) on the user's answer, explaining its strengths and weaknesses."
				),
			native_feedback_text: z
				.string()
				.describe(
					"The same detailed feedback, but translated or adapted into the user's native language ({feedback_native_language}) for better understanding."
				),
		})
		.describe(
			"Detailed feedback on the user's answer, provided in both the Q&A language and the user's native language."
		),
	score: z
		.number()
		.min(1)
		.max(5)
		.nullable()
		.describe('A score from 1 (poor) to 5 (excellent) for the answer.'),
	is_correct: z
		.boolean()
		.nullable()
		.describe(
			'A simple true/false indicating if the answer is fundamentally correct regarding the paragraph content.'
		),
	suggested_answer: z
		.string()
		.nullable()
		.describe(
			'An example of an ideal or improved answer to the question, based on the paragraph.'
		),
});

// Zod schema for evaluating all answers
const AllAnswersEvaluationSchema = z.object({
	evaluations: z
		.array(AnswerEvaluationSchema)
		.describe('An array of evaluations, one for each question-answer pair.'),
});

const TranslationEvaluationSchema = z.object({
	feedback: z
		.object({
			source_language: z
				.string()
				.describe('Overall feedback in the original text language (source_language).'),
			target_language: z
				.string()
				.describe("Overall feedback in the user's translation language (target_language)."),
		})
		.describe(
			"Overall feedback on the translation's quality, correctness, and areas for improvement, provided in both source and target languages."
		),
	score: z
		.number()
		.min(1)
		.max(10)
		.nullable()
		.describe('An overall score from 1 (poor) to 10 (excellent) for the translation.'),
	suggestions: z
		.object({
			source_language: z
				.array(z.string())
				.nullable()
				.describe(
					'Specific suggestions for improving the translation in the source_language, if any.'
				),
			target_language: z
				.array(z.string())
				.nullable()
				.describe(
					'Specific suggestions for improving the translation in the target_language, if any.'
				),
		})
		.nullable()
		.describe(
			'Specific suggestions for improving the translation, provided in both source and target languages, if any.'
		),
});

// Enhanced schema for grammar practice with word-level details
const WordTokenSchema = z.object({
	text: z.string().describe('The actual word or phrase'),
	position: z.number().describe('Position of the word in the sentence'),
	isError: z.boolean().describe('Whether this word contains an error'),
	errorType: z.string().nullable().describe('Type of error if applicable, null if no error'),
	correctedText: z
		.string()
		.nullable()
		.describe('Corrected version if this is an error, null if no error'),
	explanation: z
		.object({
			source_language: z.string().describe('Explanation in source language'),
			target_language: z.string().describe('Explanation in target language'),
		})
		.nullable()
		.describe('Explanation for the error if applicable, null if no error'),
});

const EnhancedErrorSchema = z.object({
	errorText: z.string().describe('The incorrect text found in the paragraph'),
	correctedText: z.string().describe('The corrected version of the error'),
	errorType: z.string().describe('Type of error (grammar, spelling, word choice, etc.)'),
	startPosition: z.number().describe('Start position of error in original text'),
	endPosition: z.number().describe('End position of error in original text'),
	explanation: z
		.object({
			source_language: z.string().describe('Explanation in source language'),
			target_language: z.string().describe('Explanation in target language'),
		})
		.describe('Dual-language explanations of the error and correction'),
});

const GrammarPracticeSummarySchema = z.object({
	totalWords: z.number().describe('Total number of words in the paragraph'),
	totalErrors: z.number().describe('Total number of errors found'),
	errorTypes: z.array(z.string()).describe('List of error types present'),
});

// New combined schema for grammar practice
const GrammarPracticeResponseSchema = z.object({
	paragraphs: z.array(
		z.object({
			// Backward compatibility
			paragraphWithErrors: z
				.string()
				.describe('Paragraph containing intentional errors for practice'),
			correctedParagraph: z.string().describe('The same paragraph with all errors corrected'),

			// Enhanced word-level structure
			wordTokens: z
				.array(WordTokenSchema)
				.describe('Word-by-word breakdown with error information'),

			// Enhanced error details
			allErrors: z
				.array(EnhancedErrorSchema)
				.describe('Detailed error information with positions'),

			// Summary information
			summary: GrammarPracticeSummarySchema.describe(
				'Summary statistics about the paragraph'
			),
		})
	),
});

export class LLMService {
	private openai: OpenAI | null = null;
	private gemini: GeminiService | null = null;
	private initPromise: Promise<void> | null = null;
	private cacheService: SemanticCacheService;
	private optimizationConfig: {
		caching?: { enabled: boolean };
		promptOptimization?: { enabled: boolean };
	} | null = null;

	constructor(private readonly getWordService: () => WordService) {
		this.initPromise = this.initializeProviders();
		this.cacheService = semanticCache; // Use semantic cache instead of basic cache
		this.loadOptimizationConfig();
	}

	private async loadOptimizationConfig(): Promise<void> {
		this.optimizationConfig = await getLLMOptimizationConfig();
	}

	/**
	 * Get detailed difficulty specifications for content generation
	 */
	private getDifficultySpecs(difficulty: Difficulty): DifficultySpecs {
		switch (difficulty) {
			case 'BEGINNER':
				return {
					vocabulary: {
						level: 'basic everyday words (1000 most common words)',
						wordCount: 500,
						complexity: 'simple, concrete nouns and basic verbs',
					},
					grammar: {
						structures: [
							'simple present',
							'simple past',
							'basic questions',
							'simple sentences',
						],
						complexity: 'basic sentence structures, no complex clauses',
					},
					sentenceLength: { min: 5, max: 12 },
					topics: ['daily life', 'family', 'food', 'basic activities', 'school', 'home'],
					errorTypes: [
						'spelling',
						'capitalization',
						'basic verb tense',
						'subject-verb agreement',
					],
				};
			case 'INTERMEDIATE':
				return {
					vocabulary: {
						level: 'intermediate vocabulary (2000-3000 common words)',
						wordCount: 1500,
						complexity: 'abstract concepts, phrasal verbs, idiomatic expressions',
					},
					grammar: {
						structures: [
							'present perfect',
							'conditionals',
							'passive voice',
							'complex sentences',
						],
						complexity: 'compound and complex sentences with subordinate clauses',
					},
					sentenceLength: { min: 10, max: 20 },
					topics: [
						'work',
						'education',
						'technology',
						'social issues',
						'travel',
						'culture',
					],
					errorTypes: [
						'preposition usage',
						'word form errors',
						'conjunction errors',
						'sentence fragments',
					],
				};
			case 'ADVANCED':
				return {
					vocabulary: {
						level: 'advanced vocabulary (5000+ words)',
						wordCount: 3000,
						complexity: 'sophisticated vocabulary, technical terms, nuanced meanings',
					},
					grammar: {
						structures: [
							'subjunctive mood',
							'complex conditionals',
							'advanced passive constructions',
							'sophisticated discourse markers',
						],
						complexity:
							'complex sentence structures with multiple clauses and advanced grammatical features',
					},
					sentenceLength: { min: 15, max: 30 },
					topics: [
						'academic subjects',
						'professional contexts',
						'abstract concepts',
						'cultural analysis',
						'scientific discourse',
					],
					errorTypes: [
						'reported speech',
						'conditional sentences',
						'word choice errors',
						'sentence ambiguity',
						'register appropriateness',
					],
				};
			default:
				throw new Error(`Unsupported difficulty level: ${difficulty}`);
		}
	}

	private async initializeProviders(): Promise<void> {
		const llmConfig = await getLLMConfig();

		// Initialize OpenAI if key is provided
		if (llmConfig.openAIKey) {
			this.openai = new OpenAI({ apiKey: llmConfig.openAIKey });
		}

		// Initialize Gemini if key is provided
		if (llmConfig.geminiKey) {
			this.gemini = new GeminiService();
		}
	}

	private async ensureInitialized(): Promise<{
		openai: OpenAI | null;
		gemini: GeminiService | null;
	}> {
		if (this.initPromise) {
			await this.initPromise;
			this.initPromise = null;
		}
		return { openai: this.openai, gemini: this.gemini };
	}

	/**
	 * Convert Gemini usage format to OpenAI usage format
	 */
	private convertGeminiUsageToOpenAI(geminiUsage?: {
		promptTokens: number;
		completionTokens: number;
		totalTokens: number;
	}): OpenAI.Completions.CompletionUsage | undefined {
		if (!geminiUsage) return undefined;

		return {
			prompt_tokens: geminiUsage.promptTokens,
			completion_tokens: geminiUsage.completionTokens,
			total_tokens: geminiUsage.totalTokens,
		};
	}

	/**
	 * Helper method to optimize prompts and handle caching
	 */
	private async optimizedLLMCall<T>(
		operation: string,
		templateKey: string,
		params: Record<string, unknown>,
		openaiParams: {
			temperature?: number;
			max_tokens?: number;
			response_format?: OpenAI.Chat.Completions.ChatCompletionCreateParams['response_format'];
		},
		userId?: string
	): Promise<T> {
		// Generate cache key
		const cacheKey = this.cacheService.generateLLMKey(operation, params);

		// Check semantic cache first if enabled
		if (this.optimizationConfig?.caching?.enabled) {
			const semanticResult = await semanticCache.getWithSemantic<T>(cacheKey, params);
			if (semanticResult) {
				console.log(
					`Semantic cache hit for ${operation} (similarity: ${semanticResult.similarity.toFixed(
						2
					)})`
				);
				return semanticResult.entry.value;
			}
		}

		// Optimize prompt if enabled
		let systemPrompt = typeof params.systemPrompt === 'string' ? params.systemPrompt : '';
		let optimized = false;
		let compressionRatio = 1;

		if (this.optimizationConfig?.promptOptimization?.enabled && systemPrompt) {
			try {
				const optimization = PromptOptimizerService.optimizePrompt(templateKey, params);
				systemPrompt = optimization.optimizedPrompt;
				optimized = true;
				compressionRatio = optimization.compressionRatio;
			} catch (error) {
				console.warn(`Failed to optimize prompt for ${templateKey}:`, error);
				// Fall back to original prompt
			}
		}

		// Select optimal model
		const modelSelection = modelSelector.selectModel(operation, {
			...params,
			systemPrompt,
			tokenCount: PromptOptimizerService.estimateTokens(systemPrompt),
		});

		console.log(
			`Selected model ${modelSelection.model} for ${operation}: ${modelSelection.reasoning}`
		);

		// Make API call
		const providers = await this.ensureInitialized();
		const llmConfig = await getLLMConfig();

		const startTime = Date.now();
		let completion: OpenAI.Chat.Completions.ChatCompletion;
		let responseContent: string;
		let actualInputTokens = 0;
		let actualOutputTokens = 0;

		// Determine which provider to use based on model or default provider
		const isGeminiModel = modelSelection.model.startsWith('gemini');
		const useGemini =
			isGeminiModel || (llmConfig.defaultProvider === 'gemini' && providers.gemini);

		if (useGemini && providers.gemini) {
			// Use Gemini
			const geminiResponse = await providers.gemini.createChatCompletion({
				model: modelSelection.model,
				messages: [{ role: 'system', content: systemPrompt }],
				temperature: openaiParams.temperature,
				maxTokens: openaiParams.max_tokens,
				responseFormat: openaiParams.response_format,
			});

			responseContent = geminiResponse.content;
			actualInputTokens = geminiResponse.usage?.promptTokens || 0;
			actualOutputTokens = geminiResponse.usage?.completionTokens || 0;
		} else if (providers.openai) {
			// Use OpenAI
			completion = await providers.openai.chat.completions.create({
				...openaiParams,
				model: modelSelection.model,
				messages: [{ role: 'system', content: systemPrompt }],
			});

			responseContent = completion.choices[0]?.message.content || '';
			actualInputTokens = completion.usage?.prompt_tokens || 0;
			actualOutputTokens = completion.usage?.completion_tokens || 0;
		} else {
			throw new Error('No available LLM provider configured');
		}

		const actualLatency = Date.now() - startTime;

		// Track token usage (fallback to estimation if actual usage not available)
		const inputTokens =
			actualInputTokens || PromptOptimizerService.estimateTokens(systemPrompt);
		const outputTokens =
			actualOutputTokens || PromptOptimizerService.estimateTokens(responseContent || '');

		tokenMonitor.trackUsage({
			endpoint: operation,
			operation: templateKey,
			inputTokens,
			outputTokens,
			model: modelSelection.model,
			userId,
			optimized,
			compressionRatio,
		});

		// Update model performance
		modelSelector.updateModelPerformance(
			modelSelection.model,
			actualLatency,
			-1, // Quality feedback not available here
			true // Success
		);

		// Parse result
		const result = JSON.parse(responseContent || '{}');

		// Cache result with semantic indexing if enabled
		if (this.optimizationConfig?.caching?.enabled) {
			// Get TTL without Redis dependency during build
			const ttl = this.getOptimizedTTL(templateKey);
			semanticCache.setWithSemantic(cacheKey, result, params, {
				ttl,
				tags: [operation, templateKey],
				priority: this.getCachePriority(operation),
			});
		}

		return result;
	}

	/**
	 * Get cache priority based on operation type
	 */
	private getCachePriority(operation: string): 'high' | 'normal' | 'low' {
		const highPriorityOps = ['generateWordDetails', 'evaluateTranslation'];
		const lowPriorityOps = ['generateGrammarPractice'];

		if (highPriorityOps.includes(operation)) return 'high';
		if (lowPriorityOps.includes(operation)) return 'low';
		return 'normal';
	}

	/**
	 * Get optimized TTL for cache entries without Redis dependency
	 */
	private getOptimizedTTL(contentType: string): number {
		// Static TTL values to avoid Redis dependency during build
		const ttlMap: Record<string, number> = {
			vocabulary: 7 * 24 * 60 * 60, // 7 days - stable content
			wordDetails: 7 * 24 * 60 * 60, // 7 days - stable content
			paragraphs: 3 * 24 * 60 * 60, // 3 days - semi-dynamic
			questions: 3 * 24 * 60 * 60, // 3 days - semi-dynamic
			evaluations: 30 * 24 * 60 * 60, // 30 days - very stable
			grammarPractice: 1 * 24 * 60 * 60, // 1 day - more dynamic
			default: 60 * 60 * 24, // 24 hours
		};
		return ttlMap[contentType] || ttlMap.default;
	}

	/**
	 * Process request with batch optimization
	 */
	private async batchOptimizedCall<T>(
		operation: string,
		params: Record<string, unknown>,
		priority: 'high' | 'normal' | 'low' = 'normal',
		userId?: string
	): Promise<T> {
		// Check if operation supports batching
		const batchableOps = ['generateWordDetails', 'evaluateAnswers', 'evaluateTranslation'];

		if (batchableOps.includes(operation)) {
			return batchProcessor.addToBatch<T>(operation, params, priority, 30000, userId);
		} else {
			// Fall back to direct call for non-batchable operations
			return this.directLLMCall<T>(operation, params, userId);
		}
	}

	/**
	 * Direct LLM call without batching
	 */
	private async directLLMCall<T>(
		operation: string,
		params: Record<string, unknown>,
		userId?: string
	): Promise<T> {
		// This would call the appropriate LLM method directly
		// For now, we'll use the existing optimized call
		const templateKey = this.getTemplateKey(operation);
		const openaiParams = this.getOpenAIParams(operation, params);

		return this.optimizedLLMCall<T>(operation, templateKey, params, openaiParams, userId);
	}

	/**
	 * Get template key for operation
	 */
	private getTemplateKey(operation: string): string {
		const templateMap: Record<string, string> = {
			generateRandomTerms: 'vocabulary',
			generateWordDetails: 'wordDetails',
			generateParagraph: 'paragraph',
			evaluateTranslation: 'evaluation',
			generateQuestions: 'questions',
			evaluateAnswers: 'answerEvaluation',
			generateGrammarPractice: 'grammarPractice',
		};

		return templateMap[operation] || operation;
	}

	/**
	 * Get OpenAI parameters for operation
	 */
	private getOpenAIParams(
		operation: string,
		params: Record<string, unknown>
	): {
		temperature: number;
		max_tokens: number;
	} {
		const baseParams = {
			temperature: 0.7,
			max_tokens: 1000,
		};

		// Customize based on operation
		switch (operation) {
			case 'evaluateTranslation':
			case 'evaluateAnswers':
				return { ...baseParams, temperature: 0.3 };

			case 'generateGrammarPractice':
				return { ...baseParams, temperature: 0.7, max_tokens: 2000 };

			case 'generateParagraph': {
				const count = typeof params.count === 'number' ? params.count : 1;
				return { ...baseParams, max_tokens: 1000 * count };
			}

			default:
				return baseParams;
		}
	}

	private getLanguageName(language: Language): string {
		switch (language) {
			case 'EN':
				return 'English language';
			case 'VI':
				return 'Vietnamese language';
			default:
				throw new Error(`Unsupported language: ${language}`);
		}
	}

	/**
	 * Calculate safe token limits for OpenAI models
	 */
	private calculateSafeTokenLimit(baseTokens: number, multiplier: number = 1.0): number {
		const calculatedTokens = Math.ceil(baseTokens * multiplier);
		const MAX_TOKENS_LIMIT = 16384; // OpenAI model limit
		const SAFETY_BUFFER = 500; // Leave some buffer for safety

		return Math.min(calculatedTokens, MAX_TOKENS_LIMIT - SAFETY_BUFFER);
	}

	/**
	 * Process grammar practice response to ensure enhanced structure
	 */
	private processGrammarPracticeResponse(response: {
		paragraphs?: unknown[];
	}): GrammarPracticeResultItem[] {
		if (!response?.paragraphs || !Array.isArray(response.paragraphs)) {
			throw new Error('Invalid response format');
		}

		return response.paragraphs.map((item: unknown, index: number) => {
			const typedItem = item as Partial<GrammarPracticeResultItem>;

			// Ensure backward compatibility
			if (!typedItem.paragraphWithErrors || !typedItem.correctedParagraph) {
				throw new Error(`Paragraph ${index + 1}: Missing basic structure`);
			}

			// If enhanced structure is missing, create it from basic structure
			if (!typedItem.wordTokens || !typedItem.summary) {
				console.warn(
					`Paragraph ${index + 1}: Missing enhanced structure, creating from basic data`
				);
				const enhancedItem = this.createEnhancedStructure({
					paragraphWithErrors: typedItem.paragraphWithErrors,
					correctedParagraph: typedItem.correctedParagraph,
					allErrors: typedItem.allErrors || [],
				});
				return enhancedItem;
			}

			// Validate enhanced structure
			if (!typedItem.allErrors || !Array.isArray(typedItem.allErrors)) {
				typedItem.allErrors = [];
			}

			// Ensure summary exists
			if (!typedItem.summary) {
				const words = typedItem.paragraphWithErrors
					.split(/\s+/)
					.filter((w: string) => w.length > 0);
				typedItem.summary = {
					totalWords: words.length,
					totalErrors: typedItem.allErrors.length,
					errorTypes: [...new Set(typedItem.allErrors.map((e) => e.errorType))],
				};
			}

			return typedItem as GrammarPracticeResultItem;
		});
	}

	/**
	 * Create enhanced structure from basic paragraph data
	 */
	private createEnhancedStructure(basicItem: {
		paragraphWithErrors: string;
		correctedParagraph: string;
		allErrors?: Array<{
			errorText: string;
			errorType: string;
			explanation?: {
				source_language: string;
				target_language: string;
			};
		}>;
	}): GrammarPracticeResultItem {
		const { paragraphWithErrors, correctedParagraph, allErrors = [] } = basicItem;

		// Split into words for token analysis
		const words = paragraphWithErrors.split(/\s+/).filter((w: string) => w.length > 0);
		const correctedWords = correctedParagraph.split(/\s+/).filter((w: string) => w.length > 0);

		// Create word tokens
		const wordTokens: WordToken[] = words.map((word, index) => {
			const correctedWord = correctedWords[index] || word;
			const isError = word !== correctedWord;

			// Find matching error from allErrors
			const matchingError = allErrors.find(
				(error) => error.errorText && word.includes(error.errorText.trim())
			);

			return {
				text: word,
				position: index,
				isError,
				errorType: matchingError?.errorType || null,
				correctedText: isError ? correctedWord : null,
				explanation: matchingError?.explanation || null,
			};
		});

		// Enhance allErrors with positions
		const enhancedErrors = allErrors.map((error) => {
			const startPosition = paragraphWithErrors.indexOf(error.errorText);
			const endPosition = startPosition + error.errorText.length;

			// Find corrected text from word tokens if not provided
			const correctedText =
				'correctedText' in error
					? (error as any).correctedText
					: wordTokens.find((token) => token.text.includes(error.errorText))
							?.correctedText || '';

			return {
				...error,
				correctedText,
				startPosition: Math.max(0, startPosition),
				endPosition: Math.max(startPosition, endPosition),
				explanation: error.explanation || {
					source_language: '',
					target_language: '',
				},
			};
		});

		// Create summary
		const summary = {
			totalWords: words.length,
			totalErrors: allErrors.length,
			errorTypes: [...new Set(allErrors.map((e) => e.errorType))],
		};

		return {
			...basicItem,
			wordTokens,
			allErrors: enhancedErrors,
			summary,
		};
	}

	/**
	 * Validate paragraph response and return missing count if needed
	 */
	private validateParagraphResponse(
		response: {
			paragraphs?: unknown[];
		},
		expectedCount: number,
		specs: DifficultySpecs
	): ValidationResult & { missingCount?: number } {
		const errors: string[] = [];
		const suggestions: string[] = [];

		if (!response?.paragraphs || !Array.isArray(response.paragraphs)) {
			errors.push('Response must contain a paragraphs array');
			return { isValid: false, errors, suggestions };
		}

		const actualCount = response.paragraphs.length;
		let missingCount = 0;

		if (actualCount < expectedCount) {
			missingCount = expectedCount - actualCount;
			console.log(
				`Got ${actualCount}/${expectedCount} paragraphs, will request ${missingCount} more`
			);
		} else if (actualCount > expectedCount) {
			// Trim excess paragraphs
			response.paragraphs = response.paragraphs.slice(0, expectedCount);
			console.log(`Got ${actualCount} paragraphs, trimmed to ${expectedCount}`);
		}

		// Validate each paragraph quality (but don't fail for count)
		if (response.paragraphs && Array.isArray(response.paragraphs)) {
			response.paragraphs.forEach((paragraph: unknown, index: number) => {
				if (typeof paragraph === 'string') {
					const validation = this.validateParagraphDifficulty(paragraph, specs);
					if (!validation.isValid) {
						console.warn(
							`Paragraph ${index + 1} quality issues: ${validation.errors.join(', ')}`
						);
						// Don't add to errors - just warn
					}
				}
			});
		}

		return {
			isValid: true, // Always valid, we'll handle missing count separately
			errors,
			suggestions,
			missingCount,
		};
	}

	/**
	 * Validate individual paragraph against difficulty specifications
	 */
	private validateParagraphDifficulty(
		paragraph: string,
		specs: DifficultySpecs
	): ValidationResult {
		const errors: string[] = [];
		const suggestions: string[] = [];

		if (!paragraph || paragraph.trim().length === 0) {
			errors.push('Paragraph cannot be empty');
			return { isValid: false, errors, suggestions };
		}

		const sentences = paragraph.split(/[.!?]+/).filter((s) => s.trim().length > 0);
		const words = paragraph.split(/\s+/).filter((w) => w.length > 0);

		if (sentences.length === 0) {
			errors.push('Paragraph must contain at least one sentence');
			return { isValid: false, errors, suggestions };
		}

		const avgSentenceLength = words.length / sentences.length;

		// Check sentence length constraints
		if (avgSentenceLength < specs.sentenceLength.min) {
			errors.push(
				`Average sentence length too short: ${avgSentenceLength.toFixed(1)} words (min: ${
					specs.sentenceLength.min
				})`
			);
			suggestions.push('Use more descriptive language and complex sentence structures');
		}

		if (avgSentenceLength > specs.sentenceLength.max) {
			errors.push(
				`Average sentence length too long: ${avgSentenceLength.toFixed(1)} words (max: ${
					specs.sentenceLength.max
				})`
			);
			suggestions.push('Break down complex sentences into simpler ones');
		}

		// Basic content validation
		if (words.length < 30) {
			errors.push('Paragraph too short for meaningful content');
			suggestions.push('Add more detail and context to the paragraph');
		}

		return {
			isValid: errors.length === 0,
			errors,
			suggestions,
		};
	}

	/**
	 * Validate grammar practice response and return missing count if needed
	 */
	private validateGrammarPracticeResponse(
		response: {
			paragraphs?: unknown[];
		},
		params: GrammarPracticeParams
	): ValidationResult & { missingCount?: number } {
		const errors: string[] = [];
		const suggestions: string[] = [];

		if (!response?.paragraphs || !Array.isArray(response.paragraphs)) {
			errors.push('Response must contain a paragraphs array');
			return { isValid: false, errors, suggestions };
		}

		const actualCount = response.paragraphs.length;
		let missingCount = 0;

		if (actualCount < params.count) {
			missingCount = params.count - actualCount;
			console.log(
				`Got ${actualCount}/${params.count} grammar exercises, will request ${missingCount} more`
			);
		} else if (actualCount > params.count) {
			// Trim excess paragraphs
			response.paragraphs = response.paragraphs.slice(0, params.count);
			console.log(`Got ${actualCount} grammar exercises, trimmed to ${params.count}`);
		}

		// Validate structure but don't fail for grammar error count
		response.paragraphs.forEach((item: unknown, index: number) => {
			const typedItem = item as {
				paragraphWithErrors?: string;
				correctedParagraph?: string;
				allErrors?: unknown[];
			};
			if (
				!typedItem.paragraphWithErrors ||
				!typedItem.correctedParagraph ||
				!typedItem.allErrors
			) {
				errors.push(
					`Paragraph ${
						index + 1
					}: Missing required structure (paragraphWithErrors, correctedParagraph, allErrors)`
				);
			}
			// Don't validate error requirements - just ensure basic structure
		});

		return {
			isValid: errors.length === 0,
			errors,
			suggestions,
			missingCount,
		};
	}

	/**
	 * Build enhanced paragraph prompt with detailed difficulty specifications
	 */
	private buildEnhancedParagraphPrompt(
		params: GenerateParagraphParams,
		specs: DifficultySpecs
	): string {
		const { keywords, language, difficulty, count, sentenceCount } = params;

		return `Generate EXACTLY ${count} ${this.getLanguageName(
			language
		)} paragraphs for ${difficulty.toLowerCase()} level learners.

🎯 DIFFICULTY SPECIFICATIONS:
- Vocabulary Level: ${specs.vocabulary.level}
- Grammar Complexity: ${specs.grammar.complexity}
- Required Structures: ${specs.grammar.structures.join(', ')}
- Sentence Length: ${specs.sentenceLength.min}-${specs.sentenceLength.max} words average
- Appropriate Topics: ${specs.topics.join(', ')}

📝 CONTENT REQUIREMENTS:
- Keywords to include: ${keywords.join(', ')}
- Paragraph count: EXACTLY ${count} paragraphs
${
	sentenceCount
		? `- Sentences per paragraph: approximately ${sentenceCount}`
		: '- Sentences per paragraph: 3-5 for optimal learning'
}

🔍 QUALITY STANDARDS:
- Each paragraph must be thematically coherent
- Perfect grammar and spelling
- Culturally appropriate content
- Educational value for ${difficulty.toLowerCase()} learners
- Natural flow and readability
- Use vocabulary appropriate for ${specs.vocabulary.level}
- Incorporate grammar structures: ${specs.grammar.structures.join(', ')}

⚠️ CRITICAL REQUIREMENTS:
- You MUST return exactly ${count} paragraphs
- Each paragraph must meet the specified difficulty level
- All keywords must be naturally integrated
- Content must be engaging and meaningful
- Maintain consistent difficulty throughout

📋 OUTPUT FORMAT:
Return a JSON object with exactly this structure:
{
  "paragraphs": [
    "First paragraph text here...",
    "Second paragraph text here...",
    ${count > 2 ? '// ... exactly ' + count + ' paragraphs total' : ''}
  ]
}

🎯 VERIFICATION CHECKLIST:
Before responding, verify:
✓ Exactly ${count} paragraphs generated
✓ All keywords naturally incorporated: ${keywords.join(', ')}
✓ Appropriate difficulty level maintained (${difficulty})
✓ Perfect grammar and spelling
✓ Engaging and educational content
✓ Sentence length within ${specs.sentenceLength.min}-${specs.sentenceLength.max} words average
✓ Grammar structures used: ${specs.grammar.structures.slice(0, 3).join(', ')}`;
	}

	/**
	 * Build enhanced grammar practice prompt with detailed error specifications
	 */
	private buildEnhancedGrammarPrompt(
		params: GrammarPracticeParams,
		errorRequirements: {
			description: string;
			totalErrors: number;
			mandatoryErrorTypes: Array<{
				type: string;
				minCount: number;
				examples: string[];
			}>;
		}
	): string {
		const {
			keywords,
			language,
			source_language,
			target_language,
			difficulty,
			count,
			sentenceCount,
		} = params;

		const sentenceRequirement = sentenceCount
			? `Each paragraph should have approximately ${sentenceCount} sentences.`
			: '';

		// Build mandatory error specifications
		const mandatoryErrorSpecs = errorRequirements.mandatoryErrorTypes
			.filter((errorType: { minCount: number }) => errorType.minCount > 0)
			.map((errorType: { type: string; minCount: number; examples: string[] }) => {
				const examples = errorType.examples.slice(0, 3).join(', ');
				return `- ${errorType.type.toUpperCase()}: MUST include exactly ${
					errorType.minCount
				} error(s). Examples: ${examples}`;
			})
			.join('\n');

		// Build error verification checklist
		const verificationChecklist = errorRequirements.mandatoryErrorTypes
			.filter((errorType: { minCount: number }) => errorType.minCount > 0)
			.map(
				(errorType: { minCount: number; type: string }) =>
					`✓ ${errorType.minCount} × ${errorType.type}`
			)
			.join('\n');

		return `Create ${count} grammar practice paragraphs in ${this.getLanguageName(
			language
		)} for ${difficulty.toLowerCase()} level learners.

🎯 MANDATORY ERROR REQUIREMENTS:
${errorRequirements.description}

REQUIRED ERROR DISTRIBUTION (NON-NEGOTIABLE):
${mandatoryErrorSpecs}

📋 STEP-BY-STEP GENERATION PROCESS:

STEP 1: CONTENT PLANNING
- Choose a coherent topic that naturally incorporates ALL keywords: ${keywords.join(', ')}
- Plan ${sentenceRequirement ? sentenceCount + ' sentences' : '3-5 sentences'} that flow logically
- Ensure content is engaging and appropriate for ${difficulty.toLowerCase()} learners

STEP 2: ERROR PLACEMENT STRATEGY
- Distribute exactly ${errorRequirements.totalErrors} errors across the paragraph
- MANDATORY: Include the required number of each error type specified above
- Place errors naturally within the content (avoid forced/artificial placement)
- Ensure errors don't make the content incomprehensible

STEP 3: VERIFICATION CHECKLIST
Before finalizing, verify you have included:
${verificationChecklist}

STEP 4: OUTPUT FORMATTING
For each paragraph provide:

A. BACKWARD COMPATIBILITY:
- paragraphWithErrors: The paragraph containing the specified intentional errors
- correctedParagraph: The same paragraph with ALL errors corrected

B. ENHANCED WORD-LEVEL STRUCTURE:
- wordTokens: Array of word objects with detailed information:
  * text: The actual word/phrase
  * position: Position in sentence (0-based index)
  * isError: Boolean indicating if this word has an error
  * errorType: Type of error (if applicable)
  * correctedText: Corrected version (if this is an error)
  * explanation: Dual-language explanation (if this is an error)

C. DETAILED ERROR INFORMATION:
- allErrors: Enhanced error objects with:
  * errorText: The incorrect text as it appears
  * correctedText: The corrected version
  * errorType: The specific error type
  * startPosition: Character position where error starts
  * endPosition: Character position where error ends
  * explanation: Detailed explanations in both ${this.getLanguageName(
		source_language
  )} and ${this.getLanguageName(target_language)}

D. SUMMARY STATISTICS:
- summary: Overview information:
  * totalWords: Total number of words in paragraph
  * totalErrors: Total number of errors found
  * errorTypes: Array of error types present

⚠️ CRITICAL CONSTRAINTS:
- You MUST include exactly the specified number of each required error type
- Errors must be authentic and naturally integrated into the content
- All keywords must be meaningfully incorporated: ${keywords.join(', ')}
- Content must remain coherent despite the errors
- Explanations must be educational and clear
- Generate exactly ${count} paragraphs

🔍 QUALITY ASSURANCE:
- Double-check that you have the exact number of required errors
- Ensure each error matches the specified difficulty level
- Verify that corrections are accurate and well-explained
- Confirm all keywords are naturally integrated into the content
- Ensure ${count} paragraphs are generated

📋 FINAL VERIFICATION:
✓ Exactly ${count} paragraphs generated
✓ Each paragraph has exactly ${errorRequirements.totalErrors} errors
✓ All mandatory error types included as specified
✓ All keywords incorporated: ${keywords.join(', ')}
✓ Explanations provided in both languages
✓ Content remains coherent and educational`;
	}

	async generateRandomTerms(params: GenerateRandomTermsParams): Promise<RandomWord[]> {
		const { keywordTerms, excludesTerms, maxTerms, excludeCollectionIds } = params;

		// First, try to get words from database WordNet data
		const databaseWords = await this.generateRandomTermsFromDatabase(params);

		// If we have enough words from database, return them
		if (databaseWords.length >= maxTerms) {
			return databaseWords.slice(0, maxTerms);
		}

		// If not enough words from database, supplement with LLM-generated words
		const remainingCount = maxTerms - databaseWords.length;
		const llmWords = await this.generateRandomTermsFromLLM({
			...params,
			maxTerms: remainingCount,
		});

		// Combine database words with LLM words, removing duplicates
		const allWords = [...databaseWords, ...llmWords];
		const uniqueWords = allWords.filter(
			(word, index, array) =>
				array.findIndex((w) => w.term.toLowerCase() === word.term.toLowerCase()) === index
		);

		return uniqueWords.slice(0, maxTerms);
	}

	/**
	 * Generate random terms from database WordNet data
	 */
	private async generateRandomTermsFromDatabase(
		params: GenerateRandomTermsParams
	): Promise<RandomWord[]> {
		const { keywordTerms, excludesTerms, maxTerms, excludeCollectionIds } = params;

		// Get excluded words from collections
		const allFetchedWords: Word[] = (
			await Promise.all(
				excludeCollectionIds.map(async (id) =>
					this.getWordService().getWordsByCollection(params.userId, id)
				)
			)
		).flat();

		const seenIds = new Set<string | number>();
		const excludeCollectionsWords: Word[] = allFetchedWords.filter((word) => {
			const wordId = word.id;
			if (seenIds.has(wordId)) {
				return false;
			}
			seenIds.add(wordId);
			return true;
		});

		const allExcludes = [
			...excludesTerms,
			...excludeCollectionsWords.map((word: Word) => word.term),
		];

		// Search for words in database that have WordNet data and match keywords
		const wordRepository = this.getWordRepository();
		const foundWords: Word[] = [];

		// Search for each keyword and related terms
		for (const keyword of keywordTerms) {
			// Search for words containing the keyword
			const keywordWords = await wordRepository.searchWordsWithWordNet(
				keyword,
				params.target_language,
				Math.ceil(maxTerms / keywordTerms.length) + 5 // Get extra words for filtering
			);

			foundWords.push(...keywordWords);
		}

		// Also search for words using semantic data (WordNet relationships)
		const semanticWords = await wordRepository.searchWordsBySemanticData(
			keywordTerms,
			params.target_language,
			Math.ceil(maxTerms / 2) // Get additional words from semantic search
		);

		foundWords.push(...semanticWords);

		// Remove duplicates and excluded words
		const uniqueFoundWords = foundWords.filter((word, index, array) => {
			const isUnique = array.findIndex((w) => w.id === word.id) === index;
			const isNotExcluded = !allExcludes.includes(word.term.toLowerCase());
			return isUnique && isNotExcluded;
		});

		// Convert to RandomWord format
		const randomWords: RandomWord[] = uniqueFoundWords.map((word) => ({
			term: word.term,
			language: word.language,
			difficulty: this.estimateDifficulty(word),
		}));

		// Shuffle and return requested amount
		const shuffledWords = this.shuffleArray(randomWords);
		return shuffledWords.slice(0, maxTerms);
	}

	/**
	 * Generate random terms using LLM (fallback method)
	 */
	private async generateRandomTermsFromLLM(
		params: GenerateRandomTermsParams
	): Promise<RandomWord[]> {
		const { keywordTerms, excludesTerms, maxTerms, excludeCollectionIds } = params;

		const allFetchedWords: Word[] = (
			await Promise.all(
				excludeCollectionIds.map(async (id) =>
					this.getWordService().getWordsByCollection(params.userId, id)
				)
			)
		).flat();

		const seenIds = new Set<string | number>();
		const excludeCollectionsWords: Word[] = allFetchedWords.filter((word) => {
			const wordId = word.id;
			if (seenIds.has(wordId)) {
				return false;
			}
			seenIds.add(wordId);
			return true;
		});

		const allExcludes = [
			...excludesTerms,
			...excludeCollectionsWords.map((word: Word) => word.term),
		];

		const { openai } = await this.ensureInitialized();
		const llmConfig = await getLLMConfig();

		if (!openai) {
			throw new Error('OpenAI client not initialized');
		}

		const completion = await openai.chat.completions.create({
			model: llmConfig.openAIModel,
			messages: [
				{
					role: 'system',
					content: `Generate ${maxTerms} ${this.getLanguageName(
						params.target_language
					)} vocabulary terms for ${this.getLanguageName(
						params.source_language
					)} native speakers.

Keywords: ${keywordTerms.join(', ')}
Exclude: ${allExcludes.join(', ')}
Format: lowercase (except proper nouns)
Types: mix nouns, verbs, adjectives
Level: appropriate for language learners

Return thematically consistent terms only.`,
				},
			],
			temperature: 0.6,
			max_tokens: 6000,
			response_format: zodResponseFormat(
				z.object({
					words: z.array(RandomWordSchema),
				}),
				'event'
			),
		});

		const randomWords = JSON.parse(completion.choices[0]?.message.content || '[]');
		if (!randomWords) {
			throw new Error('Failed to generate terms');
		}

		// Remove duplicates from generated words
		const words = randomWords.words as RandomWord[];
		const uniqueWords = words.filter(
			(word, index, array) =>
				array.findIndex((w) => w.term.toLowerCase() === word.term.toLowerCase()) === index
		);

		return uniqueWords;
	}

	/**
	 * Estimate difficulty of a word based on its characteristics
	 */
	private estimateDifficulty(word: Word): 'EASY' | 'MEDIUM' | 'HARD' {
		const termLength = word.term.length;
		const hasComplexWordNet =
			word.wordnet_data &&
			(word.wordnet_data.synsets.length > 3 || word.wordnet_data.hypernyms.length > 2);

		if (termLength <= 4 && !hasComplexWordNet) {
			return 'EASY';
		} else if (termLength <= 8) {
			return 'MEDIUM';
		} else {
			return 'HARD';
		}
	}

	/**
	 * Shuffle array using Fisher-Yates algorithm
	 */
	private shuffleArray<T>(array: T[]): T[] {
		const shuffled = [...array];
		for (let i = shuffled.length - 1; i > 0; i--) {
			const j = Math.floor(Math.random() * (i + 1));
			[shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
		}
		return shuffled;
	}

	async generateWordDetails(
		terms: string[],
		source_language: Language,
		target_language: Language
	): Promise<Word[]> {
		const existingWords = await this.getWordService().getWordsByTerms(terms, source_language);
		const results: Word[] = [];

		const termsToProcess = new Set(terms);

		for (const word of existingWords) {
			results.push(word);
			termsToProcess.delete(word.term);
		}

		if (termsToProcess.size > 0) {
			const { openai } = await this.ensureInitialized();
			const llmConfig = await getLLMConfig();

			if (!openai) {
				throw new Error('OpenAI client not initialized');
			}

			const completion = await openai.chat.completions.create({
				model: llmConfig.openAIModel,
				messages: [
					{
						role: 'system',
						content: `Create comprehensive word details for: ${Array.from(
							termsToProcess
						).join(', ')}

For each term:
1. IPA phonetic transcription
2. Definitions in ${this.getLanguageName(target_language)} and ${this.getLanguageName(
							source_language
						)}
3. 2-3 example sentences in both languages

Focus on accuracy and practical usage for ${this.getLanguageName(
							source_language
						)} speakers learning ${this.getLanguageName(target_language)}.

Note: WordNet semantic information (synsets, hypernyms, hyponyms, etc.) will be automatically added for English terms.`,
					},
				],
				temperature: 0.5,
				max_tokens: 12000,
				response_format: zodResponseFormat(
					z.object({
						words: z.array(OpenAIWordDetailSchema),
					}),
					'wordDetail'
				),
			});

			const details = JSON.parse(completion.choices[0]?.message.content || '{}');
			if (!details) {
				throw new Error('Failed to generate word details');
			}

			for (const wordDetail of details.words) {
				// Convert OpenAI response to RandomWordDetail format (add wordnet_data: null)
				const randomWordDetail = {
					...wordDetail,
					wordnet_data: null, // Will be automatically generated by WordService
				};

				results.push(
					await this.getWordService().createWordWithRandomWordDetail(randomWordDetail)
				);
			}
		}

		return results;
	}

	async generateParagraph(params: GenerateParagraphParams): Promise<string[]> {
		const { keywords, difficulty, count } = params;

		// Enhanced validation
		if (count < 1 || count > 10) {
			throw new Error(`Invalid count: ${count}. Must be between 1 and 10.`);
		}

		if (!keywords || keywords.length === 0) {
			throw new Error('Keywords array cannot be empty');
		}

		// Get difficulty specifications
		const difficultySpecs = this.getDifficultySpecs(difficulty);

		// Build enhanced prompt
		const systemPrompt = this.buildEnhancedParagraphPrompt(params, difficultySpecs);

		const responseSchema = z.object({
			paragraphs: z.array(z.string()),
		});

		const maxRetries = 5; // Increased retry count
		let retryCount = 0;
		let generatedParagraphs: string[] | null = null;
		let lastError: Error | null = null;

		while (retryCount < maxRetries && !generatedParagraphs) {
			try {
				const { openai } = await this.ensureInitialized();
				const llmConfig = await getLLMConfig();

				if (!openai) {
					throw new Error('OpenAI client not initialized');
				}

				// Dynamic token calculation based on difficulty and count with model limits
				const baseTokens = Math.max(difficultySpecs.sentenceLength.max * 8 * count, 1500);
				const totalTokens = this.calculateSafeTokenLimit(baseTokens);

				const completion = await openai.chat.completions.create({
					model: llmConfig.openAIModel,
					messages: [
						{
							role: 'system',
							content: systemPrompt,
						},
					],
					temperature: 0.7,
					max_tokens: totalTokens,
					response_format: zodResponseFormat(responseSchema, 'generateParagraphs'),
				});

				const parsedResponse = JSON.parse(completion.choices[0]?.message.content || '{}');

				// Enhanced validation with missing count handling
				const validation = this.validateParagraphResponse(
					parsedResponse,
					count,
					difficultySpecs
				);

				if (!validation.isValid) {
					const errorMessage = `Validation failed: ${validation.errors.join('; ')}`;
					console.warn(`Attempt ${retryCount + 1}: ${errorMessage}`);
					if (validation.suggestions.length > 0) {
						console.warn(`Suggestions: ${validation.suggestions.join('; ')}`);
					}
					throw new Error(errorMessage);
				}

				// Handle missing paragraphs by requesting more
				if (validation.missingCount && validation.missingCount > 0) {
					console.log(`Requesting ${validation.missingCount} additional paragraphs...`);

					// Generate additional paragraphs
					const additionalParams = {
						...params,
						count: validation.missingCount,
					};
					const additionalPrompt = this.buildEnhancedParagraphPrompt(
						additionalParams,
						difficultySpecs
					);

					const additionalCompletion = await openai.chat.completions.create({
						model: llmConfig.openAIModel,
						messages: [{ role: 'system', content: additionalPrompt }],
						temperature: 0.7,
						max_tokens: totalTokens,
						response_format: zodResponseFormat(responseSchema, 'generateParagraphs'),
					});

					const additionalResponse = JSON.parse(
						additionalCompletion.choices[0]?.message.content || '{}'
					);
					if (
						additionalResponse?.paragraphs &&
						Array.isArray(additionalResponse.paragraphs)
					) {
						parsedResponse.paragraphs.push(...additionalResponse.paragraphs);
						console.log(
							`Added ${additionalResponse.paragraphs.length} additional paragraphs`
						);
					}
				}

				// Ensure we have exactly the right count
				if (parsedResponse.paragraphs.length >= count) {
					generatedParagraphs = parsedResponse.paragraphs.slice(0, count);
				} else {
					throw new Error(
						`Still missing paragraphs after additional request: ${parsedResponse.paragraphs.length}/${count}`
					);
				}

				// Log success
				console.log(
					`Successfully generated ${count} paragraphs for ${difficulty} difficulty`
				);
			} catch (error) {
				retryCount++;
				lastError = error instanceof Error ? error : new Error(String(error));
				console.error(
					`Paragraph generation attempt ${retryCount}/${maxRetries} failed:`,
					lastError.message
				);

				if (retryCount >= maxRetries) {
					throw new Error(
						`Failed to generate paragraphs after ${maxRetries} attempts. Last error: ${lastError.message}`
					);
				}

				// Exponential backoff with jitter
				const baseDelay = Math.pow(2, retryCount) * 1000;
				const jitter = Math.random() * 1000;
				const delay = Math.min(baseDelay + jitter, 10000); // Cap at 10 seconds

				console.log(`Retrying in ${delay.toFixed(0)}ms...`);
				await new Promise((resolve) => setTimeout(resolve, delay));
			}
		}

		if (!generatedParagraphs) {
			throw new Error(
				`Failed to generate paragraphs after ${maxRetries} attempts. Last error: ${
					lastError?.message || 'Unknown error'
				}`
			);
		}

		return generatedParagraphs;
	}

	async generateExercises(
		params: GenerateExercisesParams
	): Promise<z.infer<typeof ExerciseSchema>[]> {
		const { paragraph, keywords, language, difficulty } = params;

		const systemPrompt = `Create 3-5 exercises from this ${this.getLanguageName(
			language
		)} paragraph for ${difficulty.toLowerCase()} learners:
"${paragraph}"

Keywords: ${keywords.join(', ')}
Types: Fill in the Blank, Multiple Choice, Matching
Test: vocabulary, grammar, comprehension

Requirements:
- Clear questions with single correct answers
- Multiple choice: 3-4 options
- Include explanations
- Progressive difficulty
- Based on paragraph content only`;

		const maxRetries = 3;
		let retryCount = 0;
		let generatedExercises: z.infer<typeof ExerciseSchema>[] | null = null;

		while (retryCount < maxRetries && !generatedExercises) {
			try {
				const { openai } = await this.ensureInitialized();
				const llmConfig = await getLLMConfig();

				if (!openai) {
					throw new Error('OpenAI client not initialized');
				}

				const completion = await openai.chat.completions.create({
					model: llmConfig.openAIModel,
					messages: [
						{
							role: 'system',
							content: systemPrompt,
						},
					],
					temperature: 0.7,
					max_tokens: 2000,
					response_format: zodResponseFormat(
						ExercisesResponseSchema,
						'generateExercises'
					),
				});

				const parsedResponse = JSON.parse(completion.choices[0]?.message.content || '{}');
				if (!parsedResponse || !parsedResponse.exercises) {
					throw new Error('Empty or invalid response from LLM for exercises.');
				}
				generatedExercises = parsedResponse.exercises;
			} catch (error) {
				retryCount++;
				console.error(`OpenAI API error (attempt ${retryCount}/${maxRetries}):`, error);
				if (retryCount >= maxRetries) {
					throw new Error(
						`Failed to generate exercises after ${maxRetries} attempts: ${
							error instanceof Error ? error.message : String(error)
						}`
					);
				}
				const delay = Math.pow(2, retryCount) * 1000;
				await new Promise((resolve) => setTimeout(resolve, delay));
			}
		}

		if (!generatedExercises) {
			throw new Error('Failed to generate exercises after retries.');
		}

		return generatedExercises;
	}

	async evaluateTranslation(
		params: EvaluateTranslationParams
	): Promise<TranslationEvaluationResult> {
		const { original_text, translated_text, source_language, target_language } = params;

		const systemPrompt = `Evaluate translation quality (1-10 scale):

Original (${this.getLanguageName(source_language)}): "${original_text}"
Translation (${this.getLanguageName(target_language)}): "${translated_text}"

Assess: accuracy, grammar, fluency, cultural appropriateness

Score guide:
- 9-10: Excellent
- 7-8: Good (minor issues)
- 5-6: Adequate (needs improvement)
- 3-4: Poor (significant issues)
- 1-2: Very poor

Provide constructive feedback in both languages with specific suggestions for improvement.`;

		try {
			const { openai } = await this.ensureInitialized();
			const llmConfig = await getLLMConfig();

			if (!openai) {
				throw new Error('OpenAI client not initialized');
			}

			const completion = await openai.chat.completions.create({
				model: llmConfig.openAIModel,
				messages: [
					{
						role: 'system',
						content: systemPrompt,
					},
				],
				temperature: 0.3,
				max_tokens: 1000,
				response_format: zodResponseFormat(
					TranslationEvaluationSchema,
					'evaluateTranslation'
				),
			});

			const evaluationResult = JSON.parse(completion.choices[0]?.message.content || '{}');

			if (!evaluationResult) {
				throw new Error('LLM did not return a valid evaluation.');
			}
			return evaluationResult as TranslationEvaluationResult;
		} catch (error) {
			console.error('Error evaluating translation with OpenAI:', error);
			throw new Error(
				`Failed to evaluate translation: ${
					error instanceof Error ? error.message : String(error)
				}`
			);
		}
	}

	async generateQuestions(params: GenerateQuestionsParams): Promise<string[]> {
		const { paragraph, language, questionCount } = params;

		// Enhanced validation
		if (questionCount < 1 || questionCount > 10) {
			throw new Error(`Invalid questionCount: ${questionCount}. Must be between 1 and 10.`);
		}

		if (!paragraph || paragraph.trim().length === 0) {
			throw new Error('Paragraph cannot be empty');
		}

		const systemPrompt = `Create ${questionCount} comprehension questions for this ${this.getLanguageName(
			language
		)} paragraph:
"${paragraph}"

🎯 QUESTION REQUIREMENTS:
- Question types: literal (30-40%), inferential (40-50%), analytical (10-20%)
- Formats: who, what, why, how (avoid yes/no questions)
- Content focus: main ideas, details, vocabulary, cause/effect relationships

📝 QUALITY STANDARDS:
- Clear wording appropriate for ${this.getLanguageName(language)} learners
- All questions must be answerable from the paragraph content only
- Varied difficulty levels to promote comprehensive understanding
- Each question should test different aspects of comprehension

⚠️ CRITICAL REQUIREMENTS:
- Generate exactly ${questionCount} questions
- Avoid repetitive question patterns
- Ensure questions are meaningful and educational
- Questions should encourage critical thinking

📋 OUTPUT FORMAT:
Return exactly ${questionCount} well-formed questions in JSON format.

🎯 VERIFICATION CHECKLIST:
✓ Exactly ${questionCount} questions generated
✓ Mix of literal, inferential, and analytical questions
✓ No yes/no questions
✓ All questions answerable from paragraph
✓ Clear and appropriate language level`;

		const maxRetries = 5; // Increased retry count
		let retryCount = 0;
		let generatedQuestions: string[] | null = null;
		let lastError: Error | null = null;

		while (retryCount < maxRetries && !generatedQuestions) {
			try {
				const { openai } = await this.ensureInitialized();
				const llmConfig = await getLLMConfig();

				if (!openai) {
					throw new Error('OpenAI client not initialized');
				}

				// Dynamic token calculation with model limits
				const baseTokens = Math.max(100 + questionCount * 80, 500);
				const totalTokens = this.calculateSafeTokenLimit(baseTokens);

				const completion = await openai.chat.completions.create({
					model: llmConfig.openAIModel,
					messages: [{ role: 'system', content: systemPrompt }],
					temperature: 0.6,
					max_tokens: totalTokens,
					response_format: zodResponseFormat(
						GeneratedQuestionsSchema,
						'generateQuestions'
					),
				});

				const parsedResponse = JSON.parse(completion.choices[0]?.message.content || '{}');

				// Enhanced validation
				if (
					!parsedResponse ||
					!parsedResponse.questions ||
					!Array.isArray(parsedResponse.questions)
				) {
					throw new Error('Invalid response format: expected questions array');
				}

				const actualCount = parsedResponse.questions.length;

				// Handle missing questions by requesting more
				if (actualCount < questionCount) {
					const missingCount = questionCount - actualCount;
					console.log(
						`Got ${actualCount}/${questionCount} questions, requesting ${missingCount} more...`
					);

					// Generate additional questions
					const additionalSystemPrompt = `Create exactly ${missingCount} additional comprehension questions for this ${this.getLanguageName(
						language
					)} paragraph:
"${paragraph}"

Requirements:
- Generate exactly ${missingCount} questions
- Avoid duplicating these existing questions: ${parsedResponse.questions
						.map((q: string, i: number) => `${i + 1}. ${q}`)
						.join('; ')}
- Follow the same quality standards as before
- Mix of literal, inferential, and analytical questions`;

					const additionalCompletion = await openai.chat.completions.create({
						model: llmConfig.openAIModel,
						messages: [{ role: 'system', content: additionalSystemPrompt }],
						temperature: 0.6,
						max_tokens: this.calculateSafeTokenLimit(missingCount * 80),
						response_format: zodResponseFormat(
							GeneratedQuestionsSchema,
							'generateQuestions'
						),
					});

					const additionalResponse = JSON.parse(
						additionalCompletion.choices[0]?.message.content || '{}'
					);
					if (
						additionalResponse?.questions &&
						Array.isArray(additionalResponse.questions)
					) {
						parsedResponse.questions.push(...additionalResponse.questions);
						console.log(
							`Added ${additionalResponse.questions.length} additional questions`
						);
					}
				}

				// Ensure we have exactly the right count and validate quality
				const finalQuestions = parsedResponse.questions.slice(0, questionCount);

				// Validate question quality (but don't fail - just warn)
				finalQuestions.forEach((q: string, index: number) => {
					if (!q || q.trim().length === 0) {
						console.warn(`Question ${index + 1} is empty`);
					} else if (q.trim().length < 10) {
						console.warn(`Question ${index + 1} is too short: "${q}"`);
					} else {
						// Check for yes/no questions
						const yesNoPattern =
							/^(is|are|do|does|did|will|would|can|could|should|has|have)\s/i;
						if (yesNoPattern.test(q.trim())) {
							console.warn(
								`Question ${index + 1} appears to be a yes/no question: "${q}"`
							);
						}
					}
				});

				if (finalQuestions.length >= questionCount) {
					generatedQuestions = finalQuestions;
				} else {
					throw new Error(
						`Still missing questions after additional request: ${finalQuestions.length}/${questionCount}`
					);
				}

				// Log success
				console.log(
					`Successfully generated ${questionCount} questions for ${language} paragraph`
				);
			} catch (error) {
				retryCount++;
				lastError = error instanceof Error ? error : new Error(String(error));
				console.error(
					`Question generation attempt ${retryCount}/${maxRetries} failed:`,
					lastError.message
				);

				if (retryCount >= maxRetries) {
					throw new Error(
						`Failed to generate questions after ${maxRetries} attempts. Last error: ${lastError.message}`
					);
				}

				// Exponential backoff with jitter
				const baseDelay = Math.pow(2, retryCount) * 1000;
				const jitter = Math.random() * 500;
				const delay = Math.min(baseDelay + jitter, 8000);

				console.log(`Retrying question generation in ${delay.toFixed(0)}ms...`);
				await new Promise((resolve) => setTimeout(resolve, delay));
			}
		}

		if (!generatedQuestions) {
			throw new Error(
				`Failed to generate questions after ${maxRetries} attempts. Last error: ${
					lastError?.message || 'Unknown error'
				}`
			);
		}

		return generatedQuestions;
	}

	async generateParagraphWithQuestions(
		params: GenerateParagraphWithQuestionsParams
	): Promise<ParagraphWithQuestionsResult> {
		const { keywords, language, difficulty, sentenceCount, questionCount } = params;

		const sentenceRequirement = sentenceCount
			? `The paragraph should have approximately ${sentenceCount} sentences.`
			: '';

		const systemPrompt = `Create 1 ${this.getLanguageName(
			language
		)} paragraph for ${difficulty.toLowerCase()} learners + ${questionCount} questions.

Paragraph:
- Keywords: ${keywords.join(', ')}
${sentenceRequirement ? `- ${sentenceRequirement}` : ''}
- Perfect grammar, ${difficulty.toLowerCase()} vocabulary
- Engaging, culturally appropriate

Questions:
- Types: literal (30-40%), inferential (40-50%), analytical (10-20%)
- Cover: main ideas, details, vocabulary, cause/effect
- Clear wording, answerable from paragraph
- Varied difficulty, no yes/no questions

Create integrated learning unit.`;

		const maxRetries = 3;
		let retryCount = 0;
		let result: ParagraphWithQuestionsResult | null = null;

		while (retryCount < maxRetries && !result) {
			try {
				const { openai } = await this.ensureInitialized();
				const llmConfig = await getLLMConfig();

				if (!openai) {
					throw new Error('OpenAI client not initialized');
				}

				const completion = await openai.chat.completions.create({
					model: llmConfig.openAIModel,
					messages: [{ role: 'system', content: systemPrompt }],
					temperature: 0.7,
					max_tokens: this.calculateSafeTokenLimit(1500 + questionCount * 100),
					response_format: zodResponseFormat(
						ParagraphWithQuestionsSchema,
						'generateParagraphWithQuestions'
					),
				});

				const parsedResponse = JSON.parse(completion.choices[0]?.message.content || '{}');

				if (
					!parsedResponse ||
					!parsedResponse.paragraph ||
					!parsedResponse.questions ||
					parsedResponse.questions.length !== questionCount
				) {
					throw new Error(
						`LLM did not return the expected format. Expected 1 paragraph and ${questionCount} questions, got paragraph: ${
							parsedResponse?.paragraph ? 'yes' : 'no'
						}, questions: ${parsedResponse?.questions?.length || 0}.`
					);
				}

				result = {
					paragraph: parsedResponse.paragraph,
					questions: parsedResponse.questions,
				};
			} catch (error) {
				retryCount++;
				console.error(
					`OpenAI API error (generateParagraphWithQuestions, attempt ${retryCount}/${maxRetries}):`,
					error
				);
				if (retryCount >= maxRetries) {
					throw new Error(
						`Failed to generate paragraph with questions after ${maxRetries} attempts: ${
							error instanceof Error ? error.message : String(error)
						}`
					);
				}
				const delay = Math.pow(2, retryCount) * 1000;
				await new Promise((resolve) => setTimeout(resolve, delay));
			}
		}

		if (!result) {
			throw new Error('Failed to generate paragraph with questions after retries.');
		}
		return result;
	}

	async evaluateAnswers(params: EvaluateAnswersParams): Promise<AnswerEvaluationResult[]> {
		const { paragraph, questions, answers, qna_language, feedback_native_language } = params;

		if (questions.length !== answers.length) {
			throw new Error('The number of questions and answers must match.');
		}
		if (questions.length === 0) {
			return [];
		}

		let questionAnswerPairs = '';
		questions.forEach((q, i) => {
			questionAnswerPairs += `Question ${i + 1}: "${q}"\nUser's Answer ${i + 1}: "${
				answers[i]
			}"\n\n`;
		});

		const systemPrompt = `Evaluate answers based on this paragraph:

Reference (${this.getLanguageName(qna_language)}): "${paragraph}"

Question-Answer Pairs:
${questionAnswerPairs}

Assess: accuracy, completeness, language quality, understanding

Score (1-5):
- 5: Excellent
- 4: Good (minor issues)
- 3: Satisfactory (some inaccuracies)
- 2: Needs improvement
- 1: Poor

Correctness: True if fundamentally accurate

Provide constructive feedback in ${this.getLanguageName(qna_language)} and ${this.getLanguageName(
			feedback_native_language
		)}. Include suggested answers when helpful.`;

		const maxRetries = 3;
		let retryCount = 0;
		let evaluationResults: AnswerEvaluationResult[] | null = null;

		while (retryCount < maxRetries && !evaluationResults) {
			try {
				const { openai } = await this.ensureInitialized();
				const llmConfig = await getLLMConfig();

				if (!openai) {
					throw new Error('OpenAI client not initialized');
				}

				const completion = await openai.chat.completions.create({
					model: llmConfig.openAIModel,
					messages: [{ role: 'system', content: systemPrompt }],
					temperature: 0.3,
					max_tokens: this.calculateSafeTokenLimit(1500 + questions.length * 300),
					response_format: zodResponseFormat(
						AllAnswersEvaluationSchema,
						'evaluateAnswers'
					),
				});

				const parsedResponse = JSON.parse(completion.choices[0]?.message.content || '{}');

				if (
					!parsedResponse ||
					!parsedResponse.evaluations ||
					parsedResponse.evaluations.length !== questions.length
				) {
					throw new Error(
						`LLM did not return the expected number of evaluations. Expected ${
							questions.length
						}, got ${parsedResponse?.evaluations?.length || 0}.`
					);
				}
				evaluationResults = parsedResponse.evaluations as AnswerEvaluationResult[];
			} catch (error) {
				retryCount++;
				console.error(
					`OpenAI API error (evaluateAnswers, attempt ${retryCount}/${maxRetries}):`,
					error
				);
				if (retryCount >= maxRetries) {
					throw new Error(
						`Failed to evaluate answers after ${maxRetries} attempts: ${
							error instanceof Error ? error.message : String(error)
						}`
					);
				}
				const delay = Math.pow(2, retryCount) * 1000;
				await new Promise((resolve) => setTimeout(resolve, delay));
			}
		}
		if (!evaluationResults) {
			throw new Error('Failed to evaluate answers after retries.');
		}
		return evaluationResults;
	}

	async generateGrammarPractice(
		params: GrammarPracticeParams
	): Promise<GrammarPracticeResultItem[]> {
		const { keywords, difficulty, count, errorDensity = 'medium' } = params;

		// Enhanced validation
		if (count < 1 || count > 5) {
			throw new Error(`Invalid count: ${count}. Must be between 1 and 5.`);
		}

		if (!keywords || keywords.length === 0) {
			throw new Error('Keywords array cannot be empty');
		}

		// Get detailed error requirements with specific distributions
		const errorRequirements = getDetailedErrorRequirements(errorDensity, difficulty);

		// Build enhanced prompt
		const systemPrompt = this.buildEnhancedGrammarPrompt(params, errorRequirements);

		const maxRetries = 5; // Increased retry count
		let retryCount = 0;
		let result: GrammarPracticeResultItem[] = [];
		let lastError: Error | null = null;

		while (retryCount < maxRetries && result.length !== count) {
			try {
				const { openai } = await this.ensureInitialized();
				const llmConfig = await getLLMConfig();

				if (!openai) {
					throw new Error('OpenAI client not initialized');
				}

				// Dynamic token calculation based on complexity with model limits
				const baseTokensPerParagraph = 2000; // Reduced base tokens
				const complexityMultiplier =
					difficulty === 'ADVANCED' ? 1.3 : difficulty === 'INTERMEDIATE' ? 1.1 : 1.0;
				const baseTokens = baseTokensPerParagraph * count;
				const totalTokens = this.calculateSafeTokenLimit(baseTokens, complexityMultiplier);

				const completion = await openai.chat.completions.create({
					model: llmConfig.openAIModel,
					messages: [{ role: 'system', content: systemPrompt }],
					temperature: 0.7,
					max_tokens: totalTokens,
					response_format: zodResponseFormat(
						GrammarPracticeResponseSchema,
						'generateGrammarPractice'
					),
				});

				const parsedResponse = JSON.parse(completion.choices[0]?.message.content || '{}');

				// Enhanced validation with missing count handling
				const validation = this.validateGrammarPracticeResponse(parsedResponse, params);

				if (!validation.isValid) {
					const errorMessage = `Grammar practice validation failed: ${validation.errors.join(
						'; '
					)}`;
					console.warn(`Attempt ${retryCount + 1}: ${errorMessage}`);
					if (validation.suggestions.length > 0) {
						console.warn(`Suggestions: ${validation.suggestions.join('; ')}`);
					}
					throw new Error(errorMessage);
				}

				// Handle missing grammar exercises by requesting more
				if (validation.missingCount && validation.missingCount > 0) {
					console.log(
						`Requesting ${validation.missingCount} additional grammar exercises...`
					);

					// Generate additional grammar exercises
					const additionalParams = {
						...params,
						count: validation.missingCount,
					};
					const additionalPrompt = this.buildEnhancedGrammarPrompt(
						additionalParams,
						errorRequirements
					);

					const additionalCompletion = await openai.chat.completions.create({
						model: llmConfig.openAIModel,
						messages: [{ role: 'system', content: additionalPrompt }],
						temperature: 0.7,
						max_tokens: totalTokens,
						response_format: zodResponseFormat(
							GrammarPracticeResponseSchema,
							'generateGrammarPractice'
						),
					});

					const additionalResponse = JSON.parse(
						additionalCompletion.choices[0]?.message.content || '{}'
					);
					if (
						additionalResponse?.paragraphs &&
						Array.isArray(additionalResponse.paragraphs)
					) {
						parsedResponse.paragraphs.push(...additionalResponse.paragraphs);
						console.log(
							`Added ${additionalResponse.paragraphs.length} additional grammar exercises`
						);
					}
				}

				// Ensure we have exactly the right count and process enhanced structure
				if (parsedResponse.paragraphs.length >= count) {
					const trimmedResponse = {
						paragraphs: parsedResponse.paragraphs.slice(0, count),
					};
					result = this.processGrammarPracticeResponse(trimmedResponse);
				} else {
					throw new Error(
						`Still missing grammar exercises after additional request: ${parsedResponse.paragraphs.length}/${count}`
					);
				}

				// Log success with details
				console.log(
					`Successfully generated ${count} grammar practice paragraphs for ${difficulty} difficulty`
				);
				console.log(
					`Error density: ${errorDensity}, Total errors per paragraph: ${errorRequirements.totalErrors}`
				);
			} catch (error) {
				retryCount++;
				lastError = error instanceof Error ? error : new Error(String(error));
				console.error(
					`Grammar practice attempt ${retryCount}/${maxRetries} failed:`,
					lastError.message
				);

				if (retryCount >= maxRetries) {
					throw new Error(
						`Failed to generate grammar practice after ${maxRetries} attempts. Last error: ${lastError.message}`
					);
				}

				// Exponential backoff with jitter
				const baseDelay = Math.pow(2, retryCount) * 1000;
				const jitter = Math.random() * 1000;
				const delay = Math.min(baseDelay + jitter, 15000); // Cap at 15 seconds for complex operations

				console.log(`Retrying grammar practice generation in ${delay.toFixed(0)}ms...`);
				await new Promise((resolve) => setTimeout(resolve, delay));
			}
		}

		if (!result || result.length === 0) {
			throw new Error(
				`Failed to generate grammar practice after ${maxRetries} attempts. Last error: ${
					lastError?.message || 'Unknown error'
				}`
			);
		}

		if (result.length !== count) {
			throw new Error(`Generated ${result.length} paragraphs but expected ${count}`);
		}

		return result;
	}

	async generateAdditionalExamples(
		params: GenerateAdditionalExamplesParams
	): Promise<Array<{ EN: string; VI: string }>> {
		const { term, source_language, target_language, existingExamples = [], count = 3 } = params;

		const maxRetries = 3;
		let retryCount = 0;
		let result: Array<{ EN: string; VI: string }> | null = null;

		// Prepare existing examples text for exclusion
		const existingExamplesText =
			existingExamples.length > 0
				? `\n\nExisting examples to avoid duplicating:\n${existingExamples
						.map((ex, i) => `${i + 1}. ${ex[source_language]} → ${ex[target_language]}`)
						.join('\n')}`
				: '';

		const systemPrompt = `Generate ${count} new practical example sentences for the ${this.getLanguageName(
			source_language
		)} word "${term}" with ${this.getLanguageName(target_language)} translations.

Requirements:
- Create diverse, realistic examples showing different contexts and meanings
- Examples should be appropriate for language learners
- Avoid overly complex or technical sentences
- Each example should clearly demonstrate the word's usage
- Provide natural, accurate translations${existingExamplesText}

Return ${count} unique examples that are different from any existing ones.`;

		while (retryCount < maxRetries && !result) {
			try {
				const { openai } = await this.ensureInitialized();
				const llmConfig = await getLLMConfig();

				if (!openai) {
					throw new Error('OpenAI client not initialized');
				}

				const completion = await openai.chat.completions.create({
					model: llmConfig.openAIModel,
					messages: [{ role: 'system', content: systemPrompt }],
					temperature: 0.7,
					max_tokens: 800,
					response_format: zodResponseFormat(
						AdditionalExamplesSchema,
						'generateAdditionalExamples'
					),
				});

				const parsedResponse = JSON.parse(completion.choices[0]?.message.content || '{}');
				if (
					!parsedResponse ||
					!parsedResponse.examples ||
					!Array.isArray(parsedResponse.examples)
				) {
					throw new Error('LLM did not return the expected examples format');
				}

				result = parsedResponse.examples;
			} catch (error) {
				retryCount++;
				console.error(
					`OpenAI API error (generateAdditionalExamples, attempt ${retryCount}/${maxRetries}):`,
					error
				);
				if (retryCount >= maxRetries) {
					throw new Error(
						`Failed to generate additional examples after ${maxRetries} attempts: ${
							error instanceof Error ? error.message : String(error)
						}`
					);
				}
				const delay = Math.pow(2, retryCount) * 1000;
				await new Promise((resolve) => setTimeout(resolve, delay));
			}
		}

		if (!result) {
			throw new Error('Failed to generate additional examples after retries.');
		}
		return result;
	}
}
